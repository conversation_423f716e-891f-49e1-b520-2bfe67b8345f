#pragma once
#include <glad/gl.h>
#include <GLFW/glfw3.h>
#include "glm/glm.hpp"

#include "Components/Light.h"
#include "Components/MeshRenderer.h"
#include "Components/Camera.h"
#include "TextureManager.h"
#include "FrameBuffer.h"
#include "Scene.h"

class Mesh;
class Light;

constexpr unsigned int MaxLightCount = 8;

#define GL_MULTISAMPLE 0x809D
#define GL_TEXTURE_CUBE_MAP_SEAMLESS 0x884F

class Renderer
{
public:
    Renderer();
    ~Renderer();
	static void Init();

	static void Render();
    static void RenderDefaultSky(Camera *camera);
    static void RenderEditorSceneView(Camera *camera, glm::vec2 viewportSize);
    static std::unordered_map<std::string, std::shared_ptr<MeshRenderer>>& GetRenderQueue() { return m_RenderQueue; }

    // Callback for rendering editor gizmos at the right time
    static void (*gizmoRenderCallback)(Camera* camera);
    static void SetGizmoRenderCallback(void (*callback)(Camera*)) { gizmoRenderCallback = callback; }
    static void Shutdown();

    static float GetWindowsDPI();

    static float GetScreenRefreshRate();
    static bool ShouldClose() { return glfwWindowShouldClose(Renderer::window); }
    static GLFWwindow* GetWindowPtr() { return Renderer::window;}
    
    static void AddMeshRenderer(std::string szObjectName, std::shared_ptr<MeshRenderer> meshRenderer);
    static void RemoveMeshRenderer(std::string szObjectName) { m_RenderQueue.erase(szObjectName); }
    static void SetWindowSize(unsigned int width, unsigned int height) { windowWidth = width; windowHeight = height; }

    static float GetWindowWidth() { return windowWidth; }
    static float GetWindowHeight() { return windowHeight; }
    static glm::vec2 GetWindowSize() { return glm::vec2(windowWidth, windowHeight); }
    static glm::vec3 GetAmbientLight() { return ambientLight; }
    static void SetAmbientLight(glm::vec3 light) { ambientLight = light; }
    static std::vector<Light*> GetLights();

    static std::unique_ptr<TextureManager>& GetTextureManager() { return m_pTextureManager; }
    static std::unique_ptr<ShaderManager>& GetShaderManager() { return m_pShaderManager; }
    static uint32_t GetCameraCount() { return m_Cameras.size(); }
    static Camera* GetCamera(int index) { return m_Cameras[index]; }
    static void AddCamera(Camera* camera) { m_Cameras.push_back(camera); }
    static void RemoveCamera(Camera* camera)
    {
        auto it = std::remove(m_Cameras.begin(), m_Cameras.end(), camera);
        if (it != m_Cameras.end())
        {
            m_Cameras.erase(it);
        }
    }
    static void ClearCameras() { m_Cameras.clear(); }

    static void OnGameObjectAdded(std::shared_ptr<GameObject> gameObject);

    // Light management
    static std::vector<Light*> GetLights();

    static glm::vec3 cameraPos;
    static glm::vec3 cameraFront;
    static glm::vec3 cameraUp;
    static glm::mat4 viewMatrix;
    static glm::mat4 projectionMatrix;


    static std::unique_ptr<FrameBuffer> m_pShadowBuffer;
    static std::unique_ptr<FrameBuffer> m_pBlurredShadowBuffer;
    static std::unique_ptr<FrameBuffer> m_pPingPongShadowBuffer;
    static glm::mat4 lightSpaceMatrix; // Keep for compatibility during transition
    static Light* testLight;


    static Scene* s_pActiveScene;
    static void SetActiveScene(Scene* scene) { s_pActiveScene = scene; }

    static GLuint quadVAO;
    static GLuint quadVBO;

    //function pointer to imgui render function
    static void (*imguiRenderFunction)();

private:
    static GLFWwindow* window;
    static unsigned int windowWidth, windowHeight;
    static bool initialized;
    static void ShadowPass(Camera* camera = nullptr);
    static void BasePass();
    static void PostFXPass();
    static void RenderShadowMapDebug(); // Debug function to show shadow map
    static void NormalPass(Camera* camera = nullptr);
    static void DepthPass(Camera* camera = nullptr);
    static void HBAOPass(Camera* camera);
    static std::unordered_map<std::string, std::shared_ptr<MeshRenderer>> m_RenderQueue;
    static std::vector<Camera*> m_Cameras;
    static glm::vec3 ambientLight;
    static std::unique_ptr<TextureManager> m_pTextureManager;
    static std::unique_ptr<FrameBuffer> m_pFrameBuffer;
    static std::unique_ptr<FrameBuffer> m_pGizmoBuffer;
    static void RenderInfiniteGrid(Camera* camera);

    static std::unique_ptr<ShaderManager> m_pShaderManager;
};

static float ClampRadiansPitch(const glm::vec3& radians, float pitchMinEuler, float pitchMaxEuler)
{
    float pitch = radians.x;
    pitch = glm::clamp(pitch, glm::radians(pitchMinEuler), glm::radians(pitchMaxEuler));
    return pitch;
}

static float ClampRadiansYaw(const glm::vec3& radians, float yawMinEuler, float yawMaxEuler)
{
    float yaw = radians.y;
    yaw = glm::clamp(yaw, glm::radians(yawMinEuler), glm::radians(yawMaxEuler));
    return yaw;
}
