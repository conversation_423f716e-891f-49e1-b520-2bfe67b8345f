#include "Renderer.h"
#include "Material.h"
#include "Mesh.h"
#include <Components/MeshRenderer.h>
#include <Components/Transform.h>
#include <stdexcept>
#include "GameTime.h"
#include "InputManager.h"
#include "Raycast3D.h"
#include <GL/gl.h>


//directional light - keeping lightSpaceMatrix for compatibility during transition
glm::mat4 Renderer::lightSpaceMatrix;

unsigned int Renderer::windowWidth;
unsigned int Renderer::windowHeight;
bool Renderer::initialized;
GLFWwindow* Renderer::window;

glm::vec3 Renderer::cameraPos(0.0f, 0.0f, 5.0f);
glm::vec3 Renderer::cameraFront(0.0f, 0.0f, -1.0f);
glm::vec3 Renderer::cameraUp(0.0f, 1.0f, 0.0f);
glm::mat4 Renderer::viewMatrix;
glm::mat4 Renderer::projectionMatrix;

glm::vec3 Renderer::ambientLight(0.3f, 0.3f, 0.38f);

std::unique_ptr<TextureManager> Renderer::m_pTextureManager;
std::unique_ptr<FrameBuffer> Renderer::m_pFrameBuffer;
std::unique_ptr<FrameBuffer> Renderer::m_pGizmoBuffer;
std::unique_ptr<FrameBuffer> Renderer::m_pShadowBuffer;


std::unique_ptr<FrameBuffer> Renderer::m_pBlurredShadowBuffer;
std::unique_ptr<FrameBuffer> Renderer::m_pPingPongShadowBuffer;
std::unique_ptr<ShaderManager>Renderer::m_pShaderManager;
std::unordered_map<std::string, std::shared_ptr<MeshRenderer>> Renderer::m_RenderQueue;
std::vector<Camera*> Renderer::m_Cameras;

Material mat;
GLuint textureID;

//quadvao
GLuint Renderer::quadVAO = 0;
GLuint Renderer::quadVBO = 0;

// Gizmo render callback
void (*Renderer::gizmoRenderCallback)(Camera*) = nullptr;

//Input
InputManager g_InputManager = InputManager::GetInstance();


Scene *Renderer::s_pActiveScene = nullptr;

static int selectedMaterial = 0;

//imgui function pointer
void (*Renderer::imguiRenderFunction)() = nullptr;


//TODO: move this to a better place
struct GizmoSphere {
    GLuint vao = 0, vbo = 0, ebo = 0;
    int indexCount = 0;
};
struct GizmoBox {
    GLuint vao = 0, vbo = 0, ebo = 0;
    int indexCount = 0;
};
struct GizmoArrow {
    GLuint vao = 0, vbo = 0, eboLines = 0, eboTris = 0;
    int lineIndexCount = 0;
    int triIndexCount = 0;
};

static GizmoArrow CreateWireArrow() {
    GizmoArrow arrow;
    // Interleaved vertex data: position (x, y, z), normal (nx, ny, nz)
    // Normals are now set to point outwards from the center of the arrowhead faces for better lighting.
    float vertices[] = {
        // Position           // Normal
        0.0f, 0.0f, 0.0f,     0.0f, -1.0f, 0.0f,   // 0: Base (shaft base, normal down)
        0.0f, 1.0f, 0.0f,     0.0f,  1.0f, 0.0f,   // 1: Tip (arrow tip, normal up)
        -0.1f, 0.8f, 0.1f,   -0.577f, 0.577f, 0.577f, // 2: Left wing (normal diagonal)
        0.1f, 0.8f, 0.1f,     0.577f, 0.577f, 0.577f, // 3: Right wing (normal diagonal)
        -0.1f, 0.8f, -0.1f,  -0.577f, 0.577f, -0.577f, // 4: Back left wing (normal diagonal)
        0.1f, 0.8f, -0.1f,    0.577f, 0.577f, -0.577f  // 5: Back right wing (normal diagonal)
    };

    GLuint lineIndices[] = {
        0, 1, // shaft
        1, 2, // tip to left wing
        1, 3, // tip to right wing
        1, 4, // tip to back left wing
        1, 5, // tip to back right wing
        2, 3, // left wing to right wing (front)
        3, 5, // right wing to back right
        5, 4, // back right to back left
        4, 2  // back left to left wing
    };

    GLuint triIndices[] = {
        1, 2, 3, // front face
        1, 3, 5, // right face
        1, 5, 4, // back right face
        1, 4, 2, // back left face
        2, 5, 3,
        2, 4, 5
    };

    glGenVertexArrays(1, &arrow.vao);
    glGenBuffers(1, &arrow.vbo);
    glGenBuffers(1, &arrow.eboLines);
    glGenBuffers(1, &arrow.eboTris);

    glBindVertexArray(arrow.vao);

    glBindBuffer(GL_ARRAY_BUFFER, arrow.vbo);
    glBufferData(GL_ARRAY_BUFFER, sizeof(vertices), vertices, GL_STATIC_DRAW);

    // Position attribute
    glEnableVertexAttribArray(0);
    glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, sizeof(float) * 6, (void*)0);
    // Normal attribute
    glEnableVertexAttribArray(1);
    glVertexAttribPointer(1, 3, GL_FLOAT, GL_FALSE, sizeof(float) * 6, (void*)(sizeof(float) * 3));

    // Wireframe EBO
    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, arrow.eboLines);
    glBufferData(GL_ELEMENT_ARRAY_BUFFER, sizeof(lineIndices), lineIndices, GL_STATIC_DRAW);

    glBindVertexArray(0);

    // Triangles EBO (not bound to VAO, bind before drawing)
    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, arrow.eboTris);
    glBufferData(GL_ELEMENT_ARRAY_BUFFER, sizeof(triIndices), triIndices, GL_STATIC_DRAW);

    arrow.lineIndexCount = sizeof(lineIndices) / sizeof(GLuint);
    arrow.triIndexCount = sizeof(triIndices) / sizeof(GLuint);
    return arrow;
}

static GizmoBox CreateWireBox() {
    GizmoBox box;
    float vertices[] = {
        -1.0f, -1.0f, -1.0f, // 0
         1.0f, -1.0f, -1.0f, // 1
         1.0f,  1.0f, -1.0f, // 2
        -1.0f,  1.0f, -1.0f, // 3
        -1.0f, -1.0f,  1.0f, // 4
         1.0f, -1.0f,  1.0f, // 5
         1.0f,  1.0f,  1.0f, // 6
        -1.0f,  1.0f,  1.0f  // 7
    };

    GLuint indices[] = {
        // Bottom face
        0, 1, 1, 5, 5, 4, 4, 0,
        // Top face
        3, 2, 2, 6, 6, 7, 7, 3,
        // Sides
        0, 3, 1, 2, 5, 6, 4, 7
    };

    glGenVertexArrays(1, &box.vao);
    glGenBuffers(1, &box.vbo);
    glGenBuffers(1, &box.ebo);

    glBindVertexArray(box.vao);
    glBindBuffer(GL_ARRAY_BUFFER, box.vbo);
    glBufferData(GL_ARRAY_BUFFER, sizeof(vertices), vertices, GL_STATIC_DRAW);
    glEnableVertexAttribArray(0);
    glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, sizeof(float) * 3 , (void*)0);

    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, box.ebo);
    glBufferData(GL_ELEMENT_ARRAY_BUFFER, sizeof(indices), indices, GL_STATIC_DRAW);

    glBindVertexArray(0);

    box.indexCount = sizeof(indices) / sizeof(GLuint);
    return box;
}

static GizmoSphere CreateWireSphere(int segments = 64) {
    std::vector<glm::vec3> vertices;
    std::vector<GLuint> indices;

    // XY circle
    for (int i = 0; i < segments; ++i) {
        float theta = 2.0f * glm::pi<float>() * float(i) / float(segments);
        float x = cos(theta);
        float y = sin(theta);
        vertices.emplace_back(x, y, 0.0f);
    }
    // XZ circle
    for (int i = 0; i < segments; ++i) {
        float theta = 2.0f * glm::pi<float>() * float(i) / float(segments);
        float x = cos(theta);
        float z = sin(theta);
        vertices.emplace_back(x, 0.0f, z);
    }
    // YZ circle
    for (int i = 0; i < segments; ++i) {
        float theta = 2.0f * glm::pi<float>() * float(i) / float(segments);
        float y = cos(theta);
        float z = sin(theta);
        vertices.emplace_back(0.0f, y, z);
    }

    // Indices for each circle (as line loops)
    for (int c = 0; c < 3; ++c) {
        int base = c * segments;
        for (int i = 0; i < segments; ++i) {
            indices.push_back(base + i);
            indices.push_back(base + ((i + 1) % segments));
        }
    }

    GizmoSphere sphere;
    glGenVertexArrays(1, &sphere.vao);
    glGenBuffers(1, &sphere.vbo);
    glGenBuffers(1, &sphere.ebo);

    glBindVertexArray(sphere.vao);
    glBindBuffer(GL_ARRAY_BUFFER, sphere.vbo);
    glBufferData(GL_ARRAY_BUFFER, vertices.size() * sizeof(glm::vec3), vertices.data(), GL_STATIC_DRAW);
    glEnableVertexAttribArray(0);
    glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, sizeof(glm::vec3), (void*)0);

    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, sphere.ebo);
    glBufferData(GL_ELEMENT_ARRAY_BUFFER, indices.size() * sizeof(GLuint), indices.data(), GL_STATIC_DRAW);

    glBindVertexArray(0);

    sphere.indexCount = static_cast<int>(indices.size());
    return sphere;
}

struct GizmoTransform {
    GizmoSphere sphere;
    GizmoBox box;
    GizmoArrow arrow;
};

static GizmoSphere g_WireSphere;
static GizmoBox g_WireBox;
static GizmoArrow g_WireArrow;
static GizmoTransform g_GizmoTransform;



Renderer::Renderer()
{
  
}

Renderer::~Renderer()
{

}


void Renderer::Init()
{
    if (initialized)
    {
	    return;
    }

    glfwInit();
    glfwWindowHint(GLFW_CONTEXT_VERSION_MAJOR, 4);
    glfwWindowHint(GLFW_CONTEXT_VERSION_MINOR, 5);
    glfwWindowHint(GLFW_OPENGL_PROFILE, GLFW_OPENGL_CORE_PROFILE);
    glfwWindowHint(GLFW_OPENGL_DEBUG_CONTEXT, GLFW_TRUE);

    float dpi = Renderer::GetWindowsDPI();
    int width = static_cast<int>(1280 * dpi);
    int height = static_cast<int>(720 * dpi);

    Renderer::window = glfwCreateWindow(width, height, "GLen", nullptr, nullptr);
    if (!Renderer::window)
        throw std::runtime_error("Error creating glfw window");
    glfwMakeContextCurrent(Renderer::window);
    glfwSwapInterval(1);

    if (!gladLoaderLoadGL())
        throw std::runtime_error("Error initializing glad");

    // glfwSetKeyCallback(Renderer::window, Input::HandleKeys);
    // glfwSetCursorPosCallback(Renderer::window, Input::HandleMouse);
    // glfwSetMouseButtonCallback(Renderer::window, Input::HandleMouseButtons);

    InputManager::GetInstance().Initialize(Renderer::window);


    glEnable(GL_MULTISAMPLE);
    glEnable(GL_DEPTH_TEST);
    glEnable(GL_TEXTURE_CUBE_MAP_SEAMLESS);
    glClearColor(0.0f, 0.0f, 0.0f, 0.0f);

    glEnable(GL_CULL_FACE);
    glEnable(GL_DEBUG_OUTPUT);

    GLFWwindow *window = Renderer::window;

    //g_Input = new Input(window);
    g_Time = new Time();

    // if(g_Input->IsKeyPressed(GLFW_KEY_ESCAPE))
    // {
    //     glfwSetWindowShouldClose(window, GLFW_TRUE);
    // }

    Renderer::initialized = true;

    ShaderManager shaderManager;
    mat.SetShader(shaderManager.GetShader("default"));

    //setup a 256 x 256 texture with an orange base and a white grid
    glGenTextures(1, &textureID);
    glBindTexture(GL_TEXTURE_2D, textureID);

    // Store the grid texture ID for billboard rendering
    unsigned char data[256 * 256 * 4];
    for (int y = 0; y < 256; y++)
    {
        for (int x = 0; x < 256; x++)
        {
            int index = (y * 256 + x) * 4;
            if (x % 32 == 0 || y % 32 == 0) // Draw grid lines every 32 pixels
            {
                data[index] = 255;     // Red
                data[index + 1] = 255; // Green
                data[index + 2] = 255; // Blue
                data[index + 3] = 255; // Alpha
            }
            else
            {
                data[index] = 255;     // Red
                data[index + 1] = 165; // Green
                data[index + 2] = 0;   // Blue
                data[index + 3] = 255; // Alpha
            }
        }
    }
    glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, 256, 256, 0, GL_RGBA, GL_UNSIGNED_BYTE, data);
    glGenerateMipmap(GL_TEXTURE_2D);
    // Set texture wrapping and filtering options
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_REPEAT);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_REPEAT);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR_MIPMAP_LINEAR);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
    glBindTexture(GL_TEXTURE_2D, 0);

    m_pTextureManager = std::make_unique<TextureManager>();


    //setup frame buffers
    m_pFrameBuffer = std::make_unique<FrameBuffer>(width, height);
    m_pGizmoBuffer = std::make_unique<FrameBuffer>(width, height);
    m_pShadowBuffer = std::make_unique<FrameBuffer>(4096, 4096, FrameBuffer::FrameBufferType::RG32F); // VSM needs RG32F
    m_pBlurredShadowBuffer = std::make_unique<FrameBuffer>(4096, 4096, FrameBuffer::FrameBufferType::RG32F); // VSM blur
    m_pPingPongShadowBuffer = std::make_unique<FrameBuffer>(4096, 4096, FrameBuffer::FrameBufferType::RG32F); // VSM ping-pong

    //setup quad for rendering frame buffer
    float quadVertices[] = {
        // positions        // texture Coords
        -1.0f, 1.0f, 0.0f, 0.0f, 1.0f,
        -1.0f, -1.0f, 0.0f, 0.0f, 0.0f,
        1.0f, -1.0f, 0.0f, 1.0f, 0.0f,

        -1.0f, 1.0f, 0.0f, 0.0f, 1.0f,
        1.0f, -1.0f, 0.0f, 1.0f, 0.0f,
        1.0f, 1.0f, 0.0f, 1.0f, 1.0f
    };

    glGenVertexArrays(1, &quadVAO);
    glGenBuffers(1, &quadVBO);
    glBindVertexArray(quadVAO);
    glBindBuffer(GL_ARRAY_BUFFER, quadVBO);
    glBufferData(GL_ARRAY_BUFFER, sizeof(quadVertices), &quadVertices, GL_STATIC_DRAW);
    glEnableVertexAttribArray(0);
    glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, 5 * sizeof(float), (void*)0);
    glEnableVertexAttribArray(1);
    glVertexAttribPointer(1, 2, GL_FLOAT, GL_FALSE, 5 * sizeof(float), (void*)(3 * sizeof(float)));
    glBindVertexArray(0);

    m_pShaderManager = std::make_unique<ShaderManager>();

    // Gizmo initialization moved to EditorGizmos

    // Billboard initialization moved to EditorGizmos
}

static const char* GetUniformOrAttributeName(int32_t id)
{
    switch(id)
    {
        case GL_FLOAT_VEC4:
            return "vec4";
        case GL_FLOAT_VEC3:
            return "vec3";
        case GL_FLOAT_VEC2:
            return "vec2";
        case GL_FLOAT:
            return "float";
        case GL_INT:
            return "int";
        case GL_BOOL:
            return "bool";
        case GL_SAMPLER_2D:
            return "sampler2D";
        case GL_SAMPLER_CUBE:
            return "samplerCube";
        default:
            return "unknown";
    }
}

static void RenderCameraPos()
{

}

static void RenderMaterialWindow(Material &mat)
{
//     ImGui::Begin("Material");
//     ImGui::Text("Shader: %s", mat.GetShader()->GetName().c_str());

//     ImGui::Separator(); // Horizontal rule between uniforms and attributes

//     ImGui::Text("Uniforms");
//     for (auto& uniform : mat.GetUniforms())
//     {
//         std::string labelPrefix = "Uniform: " + uniform.first + "##uniform";
//         ImGui::Text("Name: %s", uniform.first.c_str());
//         ImGui::Text("Type: %s", GetUniformOrAttributeName(uniform.second.uniformType));
        
//         if(uniform.second.uniformType == GL_FLOAT_VEC3)
//         {
//             glm::vec3 value = mat.GetUniformValue<glm::vec3>(uniform.first);
//             ImGui::InputFloat3((labelPrefix + "_vec3").c_str(), &value[0]);
//             mat.SetUniformValue<glm::vec3>(uniform.first, value);
//         }
//         else if(uniform.second.uniformType == GL_FLOAT_VEC4)
//         {
//             glm::vec4 value = mat.GetUniformValue<glm::vec4>(uniform.first);
//             ImGui::InputFloat4((labelPrefix + "_vec4").c_str(), &value[0]);
//             mat.SetUniformValue<glm::vec4>(uniform.first, value);
//         }
//         else if(uniform.second.uniformType == GL_SAMPLER_2D)
//         {
//             ImGui::Image((ImTextureID)(uintptr_t)mat.GetUniformValue<int>(uniform.first), ImVec2(100, 100), ImVec2(0,1), ImVec2(1,0));
//             ImGui::SameLine();
//             if (ImGui::Button(("+##plus_" + uniform.first).c_str())) {
//                 // Increment the texture ID (or index) for this uniform
//                 int texId = mat.GetUniformValue<int>(uniform.first);
//                 mat.SetUniformValue<int>(uniform.first, texId + 1);
//             }
//             ImGui::SameLine();
//             if (ImGui::Button(("-##minus_" + uniform.first).c_str())) {
//                 // Decrement the texture ID (or index) for this uniform
//                 int texId = mat.GetUniformValue<int>(uniform.first);
//                 if (texId > 0) mat.SetUniformValue<int>(uniform.first, texId - 1);
//             }
//         }
//         ImGui::Separator();
//     }
//     ImGui::End();
}

void Renderer::Render()
{
    // Resize window if needed
    static int width, height;
    glfwGetWindowSize(Renderer::GetWindowPtr(), &width, &height);

    if (width != Renderer::windowWidth || height != Renderer::windowHeight)
    {
        Renderer::windowWidth = width;
        Renderer::windowHeight = height;
        glViewport(0, 0, width, height);

        //unload framebuffers from gpu memory with glDeleteFramebuffers
        GLuint fboId = Renderer::m_pFrameBuffer->GetFBO();
        GLuint fbTexId = Renderer::m_pFrameBuffer->GetTextureID();

        //glDeleteFramebuffers(1, &fboId);
        //glDeleteTextures(1, &fbTexId);

        // Resize frame buffers
         Renderer::m_pFrameBuffer = std::make_unique<FrameBuffer>(width, height);

    }


    if(InputManager::GetInstance().IsKeyPressed(GLFW_KEY_KP_ADD))
    {
        selectedMaterial++;
        if (selectedMaterial >= 73)
        {
            selectedMaterial = 0;
        }
    }
    if(InputManager::GetInstance().IsKeyPressed(GLFW_KEY_KP_SUBTRACT))
    {
        selectedMaterial--;
        if (selectedMaterial < 0)
        {
            selectedMaterial = 0;
        }
    }

    // RENDER SHADOWS FIRST
    Renderer::ShadowPass();

    // RENDER BASE PASS TO FRAMEBUFFER
    Renderer::RenderDefaultSky(m_Cameras[1]);
    Renderer::BasePass();

    // RENDER POST FX PASS (includes shadow map debug)
    Renderer::PostFXPass();






    glEnable(GL_DEPTH_TEST);

    // Render IMGUI AFTER ALL OTHER RENDERING
    //Renderer::RenderIMGUI();

    //provide a function pointer to the ImGui render function so we can render in other .exe files
    if(imguiRenderFunction)
    {
        imguiRenderFunction();
    }

    glfwSwapBuffers(Renderer::GetWindowPtr());
}

void Renderer::RenderDefaultSky(Camera *camera)
{
    m_pFrameBuffer->Bind();
    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

    glDepthMask(GL_FALSE);
    glDisable(GL_DEPTH_TEST);

    //render the sky with "sky" shader
    auto shader = Renderer::GetShaderManager()->GetShader("sky");
    shader->Bind();

    static float timeOfDay = 0.25f;
    float aspectRatio = (float)Renderer::GetWindowWidth() / (float)Renderer::GetWindowHeight();
   

    auto cameraWorldMatrix = camera->GetGameObject()->GetTransform()->GetLocalToWorldMatrix();


    shader->SetMat4(cameraWorldMatrix, "invView");

    glm::mat4 proj = glm::perspective(glm::radians(camera->fov), aspectRatio, camera->nearClip, camera->farClip);
    glm::mat4 invProj = glm::inverse(proj);
    shader->SetMat4(invProj, "invProjection");
    /*
        uniform vec3 sunColor;
    uniform float sunIntensity;
    uniform float azimuth;   // radians
    uniform float elevation; // radians
    */
    shader->SetVec3(glm::vec3(1.0f, 1.0f, 1.0f), "sunColor");
    shader->SetFloat(1.0f, "sunIntensity");
    shader->SetFloat(glm::radians(270.0f), "azimuth");    // 270° = west, in radians
    shader->SetFloat(glm::radians(20.0f), "elevation");   // 10° above horizon, in radians
    shader->SetFloat(timeOfDay, "timeOfDay");
    shader->SetFloat(camera->fov, "fov");
    shader->SetFloat(aspectRatio, "aspectRatio");
    glBindVertexArray(quadVAO);
    glBindTexture(GL_TEXTURE_2D, m_pFrameBuffer->GetTextureID());
    glDrawArrays(GL_TRIANGLES, 0, 6);
    glBindTexture(GL_TEXTURE_2D, 0);
    glBindVertexArray(0);
    shader->Unbind();

    glDepthMask(GL_TRUE);
    glEnable(GL_DEPTH_TEST);

    // //render framebuffer to screen
    // m_pFrameBuffer->Unbind();
    // glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);
    // Renderer::GetShaderManager()->GetShader("framebuffer")->Bind();
    // glBindVertexArray(quadVAO);
    // glBindTexture(GL_TEXTURE_2D, m_pFrameBuffer->GetTextureID());
    // glDrawArrays(GL_TRIANGLES, 0, 6);
    // glBindTexture(GL_TEXTURE_2D, 0);
    // glBindVertexArray(0);
    // Renderer::GetShaderManager()->GetShader("framebuffer")->Unbind();

    // glfwSwapBuffers(Renderer::GetWindowPtr());
}

void Renderer::RenderEditorSceneView(Camera *camera, glm::vec2 viewportSize)
{
    
    ShadowPass(camera);

    static int width, height;

    if (width != viewportSize.x || height != viewportSize.y)
    {
        width = viewportSize.x;
        height = viewportSize.y;
        glViewport(0, 0, width, height);

        // Unbind any framebuffers before deleting
        glBindFramebuffer(GL_FRAMEBUFFER, 0);
        
        // Let the FrameBuffer destructor handle cleanup
        camera->m_pFrameBuffer.reset();
        camera->m_pFrameBuffer = std::make_unique<FrameBuffer>(width, height);

        camera->m_pNormalBuffer.reset();
        camera->m_pNormalBuffer = std::make_unique<FrameBuffer>(width, height);

        camera->m_pDepthBuffer.reset();
        camera->m_pDepthBuffer = std::make_unique<FrameBuffer>(width, height);

        camera->m_pHBAOBuffer.reset();
        camera->m_pHBAOBuffer = std::make_unique<FrameBuffer>(width, height);
    }

    glViewport(0, 0, width, height);

    DepthPass(camera);
    NormalPass(camera);
    //HBAOPass(camera);

    camera->m_pFrameBuffer->Bind();
    auto err = glGetError();
    if (err != GL_NO_ERROR)
    {
        std::cerr << "OpenGL Error: " << err << std::endl;
    }
    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

    glDepthMask(GL_FALSE);
    glDisable(GL_DEPTH_TEST);

    //render the sky with "sky" shader
    auto shader = Renderer::GetShaderManager()->GetShader("sky");
    shader->Bind();

    static float timeOfDay = 0.25f;
    camera->CalculateAspectRatio(viewportSize.x, viewportSize.y);
   

    auto cameraWorldMatrix = camera->GetGameObject()->GetTransform()->GetLocalToWorldMatrix();


    shader->SetMat4(cameraWorldMatrix, "invView");

    glm::mat4 proj = glm::perspective(glm::radians(camera->fov), camera->aspectRatio, camera->nearClip, camera->farClip);
    glm::mat4 invProj = glm::inverse(proj);
    shader->SetMat4(invProj, "invProjection");
    /*
        uniform vec3 sunColor;
    uniform float sunIntensity;
    uniform float azimuth;   // radians
    uniform float elevation; // radians
    */
    shader->SetVec3(glm::vec3(1.0f, 1.0f, 1.0f), "sunColor");
    shader->SetFloat(1.0f, "sunIntensity");
    shader->SetFloat(glm::radians(270.0f), "azimuth");    // 270° = west, in radians
    shader->SetFloat(glm::radians(20.0f), "elevation");   // 10° above horizon, in radians
    shader->SetFloat(timeOfDay, "timeOfDay");
    shader->SetFloat(camera->fov, "fov");
    shader->SetFloat(camera->aspectRatio, "aspectRatio");
    glBindVertexArray(quadVAO);
    glBindTexture(GL_TEXTURE_2D, camera->m_pFrameBuffer->GetTextureID());
    glDrawArrays(GL_TRIANGLES, 0, 6);
    glBindTexture(GL_TEXTURE_2D, 0);
    glBindVertexArray(0);
    shader->Unbind();

    glDepthMask(GL_TRUE);
    glEnable(GL_DEPTH_TEST);

    // Render all objects in the render queue FIRST
    for (auto object : m_RenderQueue)
    {
        object.second->Render(camera);
    }

    RenderInfiniteGrid(camera);

    // Render editor gizmos on top of the scene (if callback is set)
    if (gizmoRenderCallback)
    {
        gizmoRenderCallback(camera);
    }

    camera->m_pFrameBuffer->Unbind();

    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

    // Render IMGUI
    imguiRenderFunction();

    glfwSwapBuffers(Renderer::GetWindowPtr());
}

float Renderer::GetWindowsDPI()
{
// get the DPI of the monitor from glfw
    GLFWmonitor *monitor = glfwGetPrimaryMonitor();
    float xscale = 0.0f; 
    float yscale = 0.0f;
    glfwGetMonitorContentScale(monitor, &xscale, &yscale);
    return xscale;
}

float Renderer::GetScreenRefreshRate()
{
    GLFWmonitor *monitor = glfwGetPrimaryMonitor();
    const GLFWvidmode *mode = glfwGetVideoMode(monitor);
    return mode->refreshRate;
}

void Renderer::ShadowPass(Camera* camera)
{
    std::vector<Light*> lights;
    Light* directionalLight = nullptr;

    auto gameObjects = Renderer::s_pActiveScene->GetGameObjects();
    for (auto& gameObject : gameObjects)
    {
        auto lightComponent = gameObject->GetComponent<Light>();
        if (lightComponent)
        {
            lights.push_back(lightComponent.get());

            // Find the first active directional light with shadows enabled
            if (lightComponent->m_Type == Light::LightType::DIRECTIONAL &&
                lightComponent->m_bShadowEnabled &&
                directionalLight == nullptr)
            {
                directionalLight = lightComponent.get();
            }
        }
    }

    // Use the passed camera, or fallback to finding one
    Camera* primaryCamera = camera;

    // If no camera was passed, try to find the editor camera by name
    if (!primaryCamera)
    {
        for (Camera* cam : m_Cameras)
        {
            if (cam && cam->name == "Camera0")
            {
                primaryCamera = cam;
                break;
            }
        }
    }

    // If still no camera found, fallback to the first available camera
    if (!primaryCamera && !m_Cameras.empty())
    {
        primaryCamera = m_Cameras[0];
    }

    // Update directional light space matrix if we have one
    if (directionalLight && primaryCamera)
    {
        directionalLight->UpdateLightSpaceMatrix(primaryCamera);
        // Update the global light space matrix for compatibility
        lightSpaceMatrix = directionalLight->GetLightSpaceMatrix();
    }

    // Render all objects in the render queue
    for (auto& object : m_RenderQueue)
    {
        // Render directional light shadows if available
        if (directionalLight && primaryCamera)
        {
            object.second->RenderDirectionalDepth(directionalLight, primaryCamera);
        }

        // Render point light shadows
        object.second->RenderPointLights(lights);
    }
}

//TODO: IMPLEMENT
void Renderer::BasePass()
{

    //clear color using the first camera's background color
    if (m_Cameras.empty())
    {
        return;
    }

    // Render all objects in the render queue
    for (auto object : m_RenderQueue)
    {
        for (auto camera : m_Cameras)
        {
            camera->Update();
            object.second->Render(camera);
        }
        
    }

    m_pFrameBuffer->Unbind();
}

//TODO: IMPLEMENT
void Renderer::PostFXPass()
{
    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

    Renderer::GetShaderManager()->GetShader("framebuffer")->Bind();

    // Bind the framebuffer texture
    glBindVertexArray(quadVAO);
    glBindTexture(GL_TEXTURE_2D, m_pFrameBuffer->GetTextureID());
    glDrawArrays(GL_TRIANGLES, 0, 6);

    glBindTexture(GL_TEXTURE_2D, 0);

    glBindVertexArray(0);

    Renderer::GetShaderManager()->GetShader("framebuffer")->Unbind();


}

void Renderer::AddMeshRenderer(std::string szObjectName, std::shared_ptr<MeshRenderer> meshRenderer)
{
    //check if the object already exists
    if(m_RenderQueue.find(szObjectName) != m_RenderQueue.end())
    {
        //object already exists
        return;
    }

    m_RenderQueue[szObjectName] = meshRenderer;
}

void Renderer::OnGameObjectAdded(std::shared_ptr<GameObject> gameObject)
{

}

void Renderer::Shutdown()
{

    m_RenderQueue.clear();
    m_Cameras.clear();
    m_pTextureManager.reset();
    m_pFrameBuffer.reset();
    m_pShadowBuffer.reset();
    m_pBlurredShadowBuffer.reset();
    m_pShaderManager.reset();
}

void Renderer::RenderShadowMapDebug()
{
    // Save current viewport
    GLint viewport[4];
    glGetIntegerv(GL_VIEWPORT, viewport);

    // Disable depth testing for the debug quad
    glDisable(GL_DEPTH_TEST);

    // Set viewport for debug quad (top-right corner, 256x256)
    int debugSize = 256;
    glViewport(viewport[2] - debugSize, viewport[3] - debugSize, debugSize, debugSize);

    // Use the depth_to_color shader to visualize the shadow map
    auto depthToColorShader = GetShaderManager()->GetShader("depth_to_color");
    depthToColorShader->Bind();

    // Bind the shadow map texture
    glActiveTexture(GL_TEXTURE0);
    glBindTexture(GL_TEXTURE_2D, m_pShadowBuffer->GetTextureID());
    depthToColorShader->SetInt(0, "depthTex");

    // Render a full-screen quad
    glBindVertexArray(quadVAO);
    glDrawArrays(GL_TRIANGLES, 0, 6);
    glBindVertexArray(0);

    // Cleanup
    glBindTexture(GL_TEXTURE_2D, 0);
    depthToColorShader->Unbind();

    // Restore viewport and depth testing
    glViewport(viewport[0], viewport[1], viewport[2], viewport[3]);
    glEnable(GL_DEPTH_TEST);
}

// OLD GIZMO FUNCTIONS MOVED TO EDITOR - REMOVE THESE
/*
void Renderer::RenderGizmos(Camera* camera)
{
    if (!camera) return;

    auto lights = GetLights();

    // Calculate view and projection matrices from the provided camera
    glm::vec3 cameraPos = camera->GetGameObject()->GetTransform()->GetPosition();
    glm::vec3 cameraFront = camera->GetGameObject()->GetTransform()->GetForwardVector();
    glm::vec3 cameraUp = camera->GetGameObject()->GetTransform()->GetUpVector();
    glm::mat4 view = glm::lookAt(cameraPos, cameraPos + cameraFront, cameraUp);

    // Calculate aspect ratio from camera's framebuffer
    float aspectRatio = (float)camera->m_pFrameBuffer->GetWidth() / (float)camera->m_pFrameBuffer->GetHeight();
    glm::mat4 proj = glm::perspective(glm::radians(camera->fov), aspectRatio, camera->nearClip, camera->farClip);

    // Disable depth testing so gizmos always render on top
    glDisable(GL_DEPTH_TEST);

    auto gizmoShader = Renderer::GetShaderManager()->GetShader("gizmos");
    gizmoShader->Bind();

    // Render light gizmos (wire spheres for range + small cubes for position)
    for (auto& light : lights)
    {
        glm::vec3 pos = light->GetGameObject()->GetTransform()->GetWorldPosition();

        // Only render range gizmo for point lights (directional lights don't have range)
        if (light->m_Type == Light::LightType::POINT)
        {
            float radius = light->m_fRange;

            // Render wire sphere for light range
            glm::mat4 model = glm::translate(glm::mat4(1.0f), pos) * glm::scale(glm::mat4(1.0f), glm::vec3(radius));
            glm::mat4 mvp = proj * view * model;
            gizmoShader->SetMat4(mvp, "uMVP");
            gizmoShader->SetVec3(light->m_vColor, "mainColor"); // Use light's color for the gizmo
            gizmoShader->SetBool(false, "isSolid");
            glBindVertexArray(g_WireSphere.vao);
            glDrawElements(GL_LINES, g_WireSphere.indexCount, GL_UNSIGNED_INT, 0);
        }

        // Render small cube at light position for all light types
        glm::mat4 model = glm::translate(glm::mat4(1.0f), pos) * glm::scale(glm::mat4(1.0f), glm::vec3(0.2f));
        glm::mat4 mvp = proj * view * model;
        gizmoShader->SetMat4(mvp, "uMVP");
        gizmoShader->SetVec3(light->m_vColor, "mainColor"); // Use light's color
        gizmoShader->SetBool(true, "isSolid"); // Solid cube for better visibility
        glBindVertexArray(g_WireBox.vao);
        glDrawElements(GL_TRIANGLES, g_WireBox.indexCount, GL_UNSIGNED_INT, 0);
    }

    // Unbind gizmo shader before rendering billboards
    glBindVertexArray(0);
    gizmoShader->Unbind();

    // Render billboards for all GameObjects so users can select them
    if (s_pActiveScene)
    {
        auto gameObjects = s_pActiveScene->GetGameObjects();
        for (auto& gameObject : gameObjects)
        {
            glm::vec3 position = gameObject->GetTransform()->GetWorldPosition();

            // Choose color based on object components
            glm::vec3 billboardColor = glm::vec3(1.0f, 1.0f, 1.0f); // Default white

            if (gameObject->GetComponent<Light>())
            {
                // Yellow for lights
                billboardColor = glm::vec3(1.0f, 1.0f, 0.0f);
            }
            else if (gameObject->GetComponent<MeshRenderer>())
            {
                // Cyan for mesh renderers
                billboardColor = glm::vec3(0.0f, 1.0f, 1.0f);
            }

            RenderBillboard(position, 0.8f, view, proj, billboardColor, 0.6f);
        }
    }

    // Render special billboard at hit point if available
    if (hasHit)
    {
        RenderBillboard(hitPoint, 1.5f, view, proj, glm::vec3(0.0f, 1.0f, 0.0f), 0.9f); // Green for hit point
    }

    // Re-enable depth testing for normal rendering
    glEnable(GL_DEPTH_TEST);
}
*/

/*
void Renderer::RenderGizmos()
{
    // This is the legacy function for the main runtime renderer
    // It uses the old camera system (m_Cameras[0])
    if (m_Cameras.empty()) return;

    auto lights = GetLights();

    //calculate view matrix
    glm::vec3 cameraPos = m_Cameras[0]->GetGameObject()->GetTransform()->GetPosition();
    glm::vec3 cameraFront = m_Cameras[0]->GetGameObject()->GetTransform()->GetForwardVector();
    glm::vec3 cameraUp = m_Cameras[0]->GetGameObject()->GetTransform()->GetUpVector();
    glm::mat4 view = glm::lookAt(cameraPos, cameraPos + cameraFront, cameraUp);
    glm::mat4 proj = glm::perspective(glm::radians(m_Cameras[0]->fov), (float)Renderer::GetWindowWidth() / (float)Renderer::GetWindowHeight(), m_Cameras[0]->nearClip, m_Cameras[0]->farClip);

    glDisable(GL_DEPTH_TEST);
    auto gizmoShader = Renderer::GetShaderManager()->GetShader("gizmos");
    gizmoShader->Bind();

    // Render light gizmos
    for (auto& object : lights)
    {
        glm::vec3 pos = object->GetGameObject()->GetTransform()->GetWorldPosition();

        // Only render range gizmo for point lights
        if (object->m_Type == Light::LightType::POINT)
        {
            float radius = object->m_fRange;
            glm::mat4 model = glm::translate(glm::mat4(1.0f), pos) * glm::scale(glm::mat4(1.0f), glm::vec3(radius));
            glm::mat4 mvp = proj * view * model;
            gizmoShader->SetMat4(mvp, "uMVP");
            gizmoShader->SetVec3(object->m_vColor, "mainColor");
            gizmoShader->SetBool(false, "isSolid");
            glBindVertexArray(g_WireSphere.vao);
            glDrawElements(GL_LINES, g_WireSphere.indexCount, GL_UNSIGNED_INT, 0);
        }

        // Draw the light position indicator for all light types
        glm::mat4 model = glm::translate(glm::mat4(1.0f), pos) * glm::scale(glm::mat4(1.0f), glm::vec3(0.2f));
        glm::mat4 mvp = proj * view * model;
        gizmoShader->SetMat4(mvp, "uMVP");
        gizmoShader->SetVec3(object->m_vColor, "mainColor");
        gizmoShader->SetBool(true, "isSolid");
        glBindVertexArray(g_WireBox.vao);
        glDrawElements(GL_TRIANGLES, g_WireBox.indexCount, GL_UNSIGNED_INT, 0);
    }

    // Render hit point gizmo if available
    if (hasHit)
    {
        // Render wireframe box
        glm::mat4 model = glm::translate(glm::mat4(1.0f), hitPoint) * glm::scale(glm::mat4(1.0f), glm::vec3(0.5f));
        glm::mat4 mvp = proj * view * model;
        gizmoShader->SetMat4(mvp, "uMVP");
        gizmoShader->SetVec3(glm::vec3(0.0f, 1.0f, 0.0f), "mainColor");
        gizmoShader->SetBool(false, "isSolid");
        glBindVertexArray(g_WireBox.vao);
        glDrawElements(GL_LINES, g_WireBox.indexCount, GL_UNSIGNED_INT, 0);
    }

    // Render transform gizmos for selected object
    if(Renderer::selectedGameObjectIndex >= 0 && Renderer::selectedGameObjectIndex < s_pActiveScene->GetGameObjects().size())
    {
        auto transform = s_pActiveScene->GetGameObjects()[Renderer::selectedGameObjectIndex]->GetTransform();
        glm::vec3 pos = transform->GetPosition();

        // Draw forward arrow (red, along +Z)
        glm::mat4 model = glm::translate(glm::mat4(1.0f), pos)
              * glm::rotate(glm::mat4(1.0f), glm::radians(90.0f), glm::vec3(1, 0, 0)); // +Y to +Z
        gizmoShader->SetMat4(proj * view * model, "uMVP");
        gizmoShader->SetVec3(glm::vec3(1.0f, 0.0f, 0.0f), "mainColor");
        glBindVertexArray(g_WireArrow.vao);
        glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, g_WireArrow.eboLines);
        glDrawElements(GL_LINES, g_WireArrow.eboLines, GL_UNSIGNED_INT, 0);
        glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, g_WireArrow.eboTris);
        gizmoShader->SetBool(true, "isSolid");
        glDrawElements(GL_TRIANGLES, g_WireArrow.triIndexCount, GL_UNSIGNED_INT, 0);
        gizmoShader->SetBool(false, "isSolid");

        // Draw up arrow (green, along +Y)
        model = glm::translate(glm::mat4(1.0f), pos);
        gizmoShader->SetMat4(proj * view * model, "uMVP");
        gizmoShader->SetVec3(glm::vec3(0.0f, 1.0f, 0.0f), "mainColor");
        glBindVertexArray(g_WireArrow.vao);
        glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, g_WireArrow.eboLines);
        glDrawElements(GL_LINES, g_WireArrow.eboLines, GL_UNSIGNED_INT, 0);
        glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, g_WireArrow.eboTris);
        gizmoShader->SetBool(true, "isSolid");
        glDrawElements(GL_TRIANGLES, g_WireArrow.triIndexCount, GL_UNSIGNED_INT, 0);
        gizmoShader->SetBool(false, "isSolid");

        // Draw right arrow (blue, along +X)
        model = glm::translate(glm::mat4(1.0f), pos)
              * glm::rotate(glm::mat4(1.0f), glm::radians(-90.0f), glm::vec3(0, 0, 1)); // +Y to +X
        gizmoShader->SetMat4(proj * view * model, "uMVP");
        gizmoShader->SetVec3(glm::vec3(0.0f, 0.0f, 1.0f), "mainColor");
        glBindVertexArray(g_WireArrow.vao);
        glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, g_WireArrow.eboLines);
        glDrawElements(GL_LINES, g_WireArrow.eboLines, GL_UNSIGNED_INT, 0);
        glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, g_WireArrow.eboTris);
        gizmoShader->SetBool(true, "isSolid");
        glDrawElements(GL_TRIANGLES, g_WireArrow.triIndexCount, GL_UNSIGNED_INT, 0);
        gizmoShader->SetBool(false, "isSolid");
    }

    glBindVertexArray(0);
    gizmoShader->Unbind();

    // Render billboards for all GameObjects so users can select them
    if (s_pActiveScene)
    {
        auto gameObjects = s_pActiveScene->GetGameObjects();
        for (auto& gameObject : gameObjects)
        {
            glm::vec3 position = gameObject->GetTransform()->GetWorldPosition();

            // Choose color based on object components
            glm::vec3 billboardColor = glm::vec3(1.0f, 1.0f, 1.0f); // Default white

            if (gameObject->GetComponent<Light>())
            {
                // Yellow for lights
                billboardColor = glm::vec3(1.0f, 1.0f, 0.0f);
            }
            else if (gameObject->GetComponent<MeshRenderer>())
            {
                // Cyan for mesh renderers
                billboardColor = glm::vec3(0.0f, 1.0f, 1.0f);
            }

            RenderBillboard(position, 0.8f, view, proj, billboardColor, 0.6f);
        }
    }

    // Render special billboard at hit point if available
    if (hasHit)
    {
        RenderBillboard(hitPoint, 1.5f, view, proj, glm::vec3(0.0f, 1.0f, 0.0f), 0.9f); // Green for hit point
    }
}

// Helper function to recursively search for lights in a GameObject and its children
static void CollectLightsRecursive(const std::shared_ptr<GameObject>& gameObject, std::vector<Light*>& lights)
{
    // Check if this GameObject has a Light component
    auto lightComponent = gameObject->GetComponent<Light>();
    if (lightComponent)
    {
        lights.push_back(lightComponent.get());
    }

    // Recursively search children
    const auto& children = gameObject->GetChildren();
    for (const auto& child : children)
    {
        CollectLightsRecursive(child, lights);
    }
}

std::vector<Light*> Renderer::GetLights()
{
    std::vector<Light*> lights;

    if (!s_pActiveScene) return lights;

    // Search through all root GameObjects and their children recursively
    auto gameObjects = s_pActiveScene->GetGameObjects();
    for (auto& gameObject : gameObjects)
    {
        CollectLightsRecursive(gameObject, lights);
    }

    return lights;
}

void Renderer::HandleMousePicking()
{
    // Only handle left mouse clicks (button 0)
    if (!ImGui::IsMouseClicked(0) || m_Cameras.empty()) return;

    ImVec2 mousePos = ImGui::GetMousePos();
    int winWidth = GetWindowWidth();
    int winHeight = GetWindowHeight();

    float x = (2.0f * mousePos.x) / winWidth - 1.0f;
    float y = 1.0f - (2.0f * mousePos.y) / winHeight;

    // Calculate view and projection matrices
    glm::vec3 cameraPos = m_Cameras[0]->GetGameObject()->GetTransform()->GetPosition();
    glm::vec3 cameraFront = m_Cameras[0]->GetGameObject()->GetTransform()->GetForwardVector();
    glm::vec3 cameraUp = m_Cameras[0]->GetGameObject()->GetTransform()->GetUpVector();
    glm::mat4 view = glm::lookAt(cameraPos, cameraPos + cameraFront, cameraUp);
    glm::mat4 proj = glm::perspective(glm::radians(m_Cameras[0]->fov), (float)winWidth / (float)winHeight, m_Cameras[0]->nearClip, m_Cameras[0]->farClip);

    glm::vec4 ray_clip = glm::vec4(x, y, -1.0f, 1.0f);
    glm::vec4 ray_eye = glm::inverse(proj) * ray_clip;
    ray_eye = glm::vec4(ray_eye.x, ray_eye.y, -1.0f, 0.0f);

    glm::vec3 ray_world = glm::normalize(glm::vec3(glm::inverse(view) * ray_eye));
    glm::vec3 ray_origin = cameraPos;

    // First, check for billboard intersections (since they're always on top)
    if (s_pActiveScene)
    {
        auto gameObjects = s_pActiveScene->GetGameObjects();
        float billboardSize = 0.8f; // Same size as rendered billboards

        for (size_t i = 0; i < gameObjects.size(); ++i)
        {
            glm::vec3 objPos = gameObjects[i]->GetTransform()->GetWorldPosition();

            // Calculate distance from ray to billboard center
            glm::vec3 toObject = objPos - ray_origin;
            float projLength = glm::dot(toObject, ray_world);

            if (projLength > 0) // Object is in front of camera
            {
                glm::vec3 closestPoint = ray_origin + ray_world * projLength;
                float distance = glm::length(closestPoint - objPos);

                // Check if ray hits the billboard (using a slightly larger hit area for easier selection)
                if (distance <= billboardSize * 0.6f) // 60% of billboard size for hit detection
                {
                    selectedGameObjectIndex = static_cast<int>(i);
                    hasHit = true;
                    hitPoint = objPos;

                    // Notify editor of selection change
                    if (onSelectionChanged)
                        onSelectionChanged(selectedGameObjectIndex);

                    return; // Billboard hit takes priority
                }
            }
        }
    }

    // If no billboard was hit, check for geometry intersections
    float closestT = std::numeric_limits<float>::max();
    glm::vec3 closestHit;
    int hitObjectIndex = -1;

    for (auto& pair : m_RenderQueue)
    {
        auto& meshRenderer = pair.second;
        Mesh* mesh = meshRenderer->GetMesh().get();
        if (!mesh) continue;

        // Get world transform
        glm::mat4 model = meshRenderer->GetGameObject()->GetTransform()->GetLocalToWorldMatrix();

        // Find the index of this GameObject in the scene
        int objectIndex = -1;
        if (s_pActiveScene)
        {
            auto gameObjects = s_pActiveScene->GetGameObjects();
            for (size_t i = 0; i < gameObjects.size(); ++i)
            {
                if (gameObjects[i].get() == meshRenderer->GetGameObject())
                {
                    objectIndex = static_cast<int>(i);
                    break;
                }
            }
        }

        if (objectIndex == -1) continue; // GameObject not found in scene

        // Check each submesh
        for (int i = 0; i < mesh->subMeshes.size(); ++i)
        {
            auto& subMesh = mesh->subMeshes[i];
            auto& vertices = subMesh.vertices;
            auto& indices = subMesh.indices;

            for (size_t j = 0; j < indices->size(); j += 3)
            {
                glm::vec3 v0 = model * glm::vec4((*vertices)[(*indices)[j]], 1.0f);
                glm::vec3 v1 = model * glm::vec4((*vertices)[(*indices)[j + 1]], 1.0f);
                glm::vec3 v2 = model * glm::vec4((*vertices)[(*indices)[j + 2]], 1.0f);

                float t;
                if (Raycast3D::RayIntersectsTriangle(ray_origin, ray_world, v0, v1, v2, t))
                {
                    if (t < closestT)
                    {
                        closestT = t;
                        closestHit = ray_origin + ray_world * t;
                        hitObjectIndex = objectIndex;
                    }
                }
            }
        }
    }

    if (hitObjectIndex != -1)
    {
        selectedGameObjectIndex = hitObjectIndex;
        hitPoint = closestHit;
        hasHit = true;

        // Notify editor of selection change
        if (onSelectionChanged)
            onSelectionChanged(selectedGameObjectIndex);
    }
    else
    {
        // No hit - clear selection
        selectedGameObjectIndex = -1;
        hasHit = false;

        // Notify editor of selection change
        if (onSelectionChanged)
            onSelectionChanged(selectedGameObjectIndex);
    }
}

void Renderer::RenderBillboard(const glm::vec3& position, float size, const glm::mat4& view, const glm::mat4& projection, const glm::vec3& color, float alpha)
{
    // Get the billboard shader
    auto billboardShader = GetShaderManager()->GetShader("billboard");
    if (!billboardShader) return;

    // Enable blending for transparency
    glEnable(GL_BLEND);
    glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);

    // Depth testing is already managed by the gizmo system
    glDepthMask(GL_FALSE);

    billboardShader->Bind();

    // Set uniforms
    billboardShader->SetMat4(view, "uView");
    billboardShader->SetMat4(projection, "uProjection");
    billboardShader->SetVec3(position, "uBillboardPos");
    billboardShader->SetFloat(size, "uBillboardSize");
    billboardShader->SetVec3(color, "uColor");
    billboardShader->SetFloat(alpha, "uAlpha");

    // Bind the grid texture
    glActiveTexture(GL_TEXTURE0);
    glBindTexture(GL_TEXTURE_2D, gridTextureID);
    billboardShader->SetInt(0, "uTexture");

    // Render the billboard quad
    glBindVertexArray(billboardVAO);
    glDrawElements(GL_TRIANGLES, 6, GL_UNSIGNED_INT, 0);
    glBindVertexArray(0);

    // Unbind texture
    glBindTexture(GL_TEXTURE_2D, 0);

    billboardShader->Unbind();

    // Restore depth writing and disable blending
    glDepthMask(GL_TRUE);
    glDisable(GL_BLEND);
}
*/
void Renderer::NormalPass(Camera* camera)
{
    if (!camera || !camera->m_pNormalBuffer) return;

    auto normalShader = GetShaderManager()->GetShader("normal");
    if (!normalShader) return;

    camera->m_pNormalBuffer->Bind();
    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);
    
    normalShader->Bind();

    // Set camera matrices
    auto transform = camera->GetGameObject()->GetTransform();
    auto cameraPos = transform->GetPosition();
    auto cameraFront = transform->GetForwardVector();
    auto cameraUp = transform->GetUpVector();
    
    glm::mat4 view = glm::lookAt(cameraPos, cameraPos + cameraFront, cameraUp);
    glm::mat4 proj = glm::perspective(glm::radians(camera->fov), camera->aspectRatio, camera->nearClip, camera->farClip);
    
    normalShader->SetMat4(view, "view");
    normalShader->SetMat4(proj, "projection");

    // Render all objects with normal shader - simplified version
    for (auto& object : m_RenderQueue)
    {
        if (!object.second || !object.second->GetMesh()) continue;
        
        glm::mat4 model = object.second->GetGameObject()->GetTransform()->GetLocalToWorldMatrix();
        normalShader->SetMat4(model, "model");
        
        // Render each submesh directly instead of using DrawMesh
        auto mesh = object.second->GetMesh();
        int meshIndex = 0;
        for (const auto& subMesh : mesh->subMeshes)
        {
            glBindVertexArray(subMesh.VAO);
            
            // Bind albedo texture for transparency testing
            glActiveTexture(GL_TEXTURE0);
            auto material = object.second->GetMaterialAtIndex(meshIndex);
            if (material) {
                glBindTexture(GL_TEXTURE_2D, material->GetUniformValue<int>("albedo"));
            }
            normalShader->SetInt(0, "albedo");
            normalShader->SetBool(subMesh.isAlphaTest, "discardTransparent");
            
            glDrawElements(GL_TRIANGLES, subMesh.indices->size(), GL_UNSIGNED_INT, 0);
            glBindTexture(GL_TEXTURE_2D, 0);
            meshIndex++;
        }
    }

    normalShader->Unbind();
    camera->m_pNormalBuffer->Unbind();
}

void Renderer::DepthPass(Camera* camera)
{
    if (!camera || !camera->m_pDepthBuffer) return;

    auto depthShader = GetShaderManager()->GetShader("depth_camera");
    if (!depthShader) return;

    camera->m_pDepthBuffer->Bind();
    
    // Check framebuffer status
    GLenum status = glCheckFramebufferStatus(GL_FRAMEBUFFER);
    if (status != GL_FRAMEBUFFER_COMPLETE) {
        std::cerr << "Depth framebuffer not complete: " << status << std::endl;
        camera->m_pDepthBuffer->Unbind();
        return;
    }
    
    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);
    
    // Enable depth testing and writing
    glEnable(GL_DEPTH_TEST);
    glDepthMask(GL_TRUE);
    glDepthFunc(GL_LESS);
    
    depthShader->Bind();

    // Set camera matrices
    auto transform = camera->GetGameObject()->GetTransform();
    auto cameraPos = transform->GetPosition();
    auto cameraFront = transform->GetForwardVector();
    auto cameraUp = transform->GetUpVector();
    
    glm::mat4 view = glm::lookAt(cameraPos, cameraPos + cameraFront, cameraUp);
    glm::mat4 proj = glm::perspective(glm::radians(camera->fov), camera->aspectRatio, camera->nearClip, camera->farClip);
    
    depthShader->SetMat4(view, "view");
    depthShader->SetMat4(proj, "projection");
    depthShader->SetFloat(camera->nearClip, "near_plane");
    depthShader->SetFloat(camera->farClip, "far_plane");

    // Render all objects with depth shader
    for (auto& object : m_RenderQueue)
    {
        if (!object.second || !object.second->GetMesh()) continue;
        
        glm::mat4 model = object.second->GetGameObject()->GetTransform()->GetLocalToWorldMatrix();
        depthShader->SetMat4(model, "model");
        
        // Render each submesh
        auto mesh = object.second->GetMesh();
        int meshIndex = 0;
        for (const auto& subMesh : mesh->subMeshes)
        {
            glBindVertexArray(subMesh.VAO);
            
            // Bind albedo texture for alpha testing
            glActiveTexture(GL_TEXTURE0);
            auto material = object.second->GetMaterialAtIndex(meshIndex);
            if (material) {
                glBindTexture(GL_TEXTURE_2D, material->GetUniformValue<int>("albedo"));
            }
            depthShader->SetInt(0, "albedo");
            depthShader->SetBool(subMesh.isAlphaTest, "discardTransparent");
            
            glDrawElements(GL_TRIANGLES, subMesh.indices->size(), GL_UNSIGNED_INT, 0);
            glBindTexture(GL_TEXTURE_2D, 0);
            meshIndex++;
        }
    }
    
    depthShader->Unbind();
    camera->m_pDepthBuffer->Unbind();
}

void Renderer::HBAOPass(Camera* camera)
{
    if (!camera || !camera->m_pHBAOBuffer || !camera->m_pDepthBuffer || !camera->m_pNormalBuffer) return;

    auto hbaoShader = GetShaderManager()->GetShader("hbao");
    if (!hbaoShader) return;

    camera->m_pHBAOBuffer->Bind();
    glClear(GL_COLOR_BUFFER_BIT);
    
    // Disable depth testing for fullscreen quad
    glDisable(GL_DEPTH_TEST);
    glDepthMask(GL_FALSE);
    
    hbaoShader->Bind();

    // Set camera matrices for view space calculations
    auto transform = camera->GetGameObject()->GetTransform();
    auto cameraPos = transform->GetPosition();
    auto cameraFront = transform->GetForwardVector();
    auto cameraUp = transform->GetUpVector();
    
    glm::mat4 view = glm::lookAt(cameraPos, cameraPos + cameraFront, cameraUp);
    glm::mat4 proj = glm::perspective(glm::radians(camera->fov), camera->aspectRatio, camera->nearClip, camera->farClip);
    
    hbaoShader->SetMat4(view, "view");
    hbaoShader->SetMat4(proj, "projection");
    glm::vec2 screenSize(camera->m_pDepthBuffer->GetWidth(), camera->m_pDepthBuffer->GetHeight());
    hbaoShader->SetVec2(screenSize, "screenSize");

    // HBAO parameters
    hbaoShader->SetFloat(camera->hbaoRadius, "radius");        // Reduced from 1.0
    hbaoShader->SetFloat(camera->hbaoBias, "bias");         // Reduced from 0.025
    hbaoShader->SetFloat(camera->hbaoIntensity, "intensity");     // Reduced from 1.0
    hbaoShader->SetInt(camera->hbaoDirections, "numDirections");      // Reduced from 8
    hbaoShader->SetInt(camera->hbaoSteps, "numSteps");           // Reduced from 4

    // Bind depth and normal textures
    glActiveTexture(GL_TEXTURE0);
    glBindTexture(GL_TEXTURE_2D, camera->m_pDepthBuffer->GetTextureID());
    hbaoShader->SetInt(0, "gDepth");

    glActiveTexture(GL_TEXTURE1);
    glBindTexture(GL_TEXTURE_2D, camera->m_pNormalBuffer->GetTextureID());
    hbaoShader->SetInt(1, "gNormal");

    // Render fullscreen quad
    glBindVertexArray(quadVAO);
    glDrawArrays(GL_TRIANGLES, 0, 6);
    glBindVertexArray(0);

    // Cleanup
    glBindTexture(GL_TEXTURE_2D, 0);
    glActiveTexture(GL_TEXTURE0);
    glBindTexture(GL_TEXTURE_2D, 0);
    
    hbaoShader->Unbind();
    
    // Re-enable depth testing
    glEnable(GL_DEPTH_TEST);
    glDepthMask(GL_TRUE);
    
    camera->m_pHBAOBuffer->Unbind();
}

void Renderer::RenderInfiniteGrid(Camera* camera)
{
    auto gridShader = GetShaderManager()->GetShader("infinite_grid");
    if (!gridShader) {
        std::cout << "Grid shader not found!" << std::endl;
        return;
    }

    // Create quad VAO if it doesn't exist
    static GLuint gridQuadVAO = 0;
    if (gridQuadVAO == 0) {
        float quadVertices[] = {
            -1.0f,  1.0f, 0.0f, 0.0f, 1.0f,
            -1.0f, -1.0f, 0.0f, 0.0f, 0.0f,
             1.0f, -1.0f, 0.0f, 1.0f, 0.0f,
            -1.0f,  1.0f, 0.0f, 0.0f, 1.0f,
             1.0f, -1.0f, 0.0f, 1.0f, 0.0f,
             1.0f,  1.0f, 0.0f, 1.0f, 1.0f
        };
        
        GLuint quadVBO;
        glGenVertexArrays(1, &gridQuadVAO);
        glGenBuffers(1, &quadVBO);
        glBindVertexArray(gridQuadVAO);
        glBindBuffer(GL_ARRAY_BUFFER, quadVBO);
        glBufferData(GL_ARRAY_BUFFER, sizeof(quadVertices), quadVertices, GL_STATIC_DRAW);
        glEnableVertexAttribArray(0);
        glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, 5 * sizeof(float), (void*)0);
        glEnableVertexAttribArray(1);
        glVertexAttribPointer(1, 2, GL_FLOAT, GL_FALSE, 5 * sizeof(float), (void*)(3 * sizeof(float)));
    }

    // Enable depth testing but disable depth writing
    glEnable(GL_DEPTH_TEST);
    glDepthMask(GL_FALSE);
    glEnable(GL_BLEND);
    glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);

    gridShader->Bind();

    // Calculate matrices
    auto transform = camera->GetGameObject()->GetTransform();
    glm::vec3 cameraPos = transform->GetPosition();
    glm::vec3 cameraFront = transform->GetForwardVector();
    glm::vec3 cameraUp = transform->GetUpVector();
    
    glm::mat4 view = glm::lookAt(cameraPos, cameraPos + cameraFront, cameraUp);
    glm::mat4 proj = glm::perspective(glm::radians(camera->fov), camera->aspectRatio, camera->nearClip, camera->farClip);

    // Set uniforms
    gridShader->SetMat4(view, "view");
    gridShader->SetMat4(proj, "projection");
    gridShader->SetVec3(cameraPos, "cameraPos");
    gridShader->SetFloat(1.0f, "gridScale");
    gridShader->SetVec3(glm::vec3(0.5f, 0.5f, 0.5f), "gridColor");
    gridShader->SetFloat(0.5f, "gridAlpha");

    // Render fullscreen quad
    glBindVertexArray(gridQuadVAO);
    glDrawArrays(GL_TRIANGLES, 0, 6);
    glBindVertexArray(0);

    gridShader->Unbind();
    
    // Restore render state
    glDepthMask(GL_TRUE);
    glDisable(GL_BLEND);
}

std::vector<Light*> Renderer::GetLights()
{
    std::vector<Light*> lights;
    auto gameObjects = Renderer::s_pActiveScene->GetGameObjects();
    for (auto& gameObject : gameObjects)
    {
        auto lightComponent = gameObject->GetComponent<Light>();
        if (lightComponent)
        {
            lights.push_back(lightComponent.get());
        }
    }
    return lights;
}